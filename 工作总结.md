# 工作总结

## Sheet1

| 2020半年   | Unnamed: 1                                                               | Unnamed: 2                                                                                                                                                                                                                          |
|:---------|:-------------------------------------------------------------------------|:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1        | 开发能够实现缺陷面积检测的神经网络                                                        | 开发完成，检测效果与海康威视、猎户座的检测效果相当，因类单晶改造暂停，暂无客户端使用                                                                                                                                                                                          |
| 2        | 神经网络检测速度提升(实现高分辨率检测或者降成本)                                                | 已能实现7倍左右的提速，检测时间可降至100ms以内，下半年可在客户端实现高分辨率检测，也可考虑对工作站进一步降本                                                                                                                                                                           |
| 3        | 软件组工作中遇到的关键技术难点解决                                                        | 3D翘曲数据分析、算法研究；3D判定硅片线痕方向算法；软件锁机功能设计；上位机卡顿分析及解决；TCP通信优化；各现场直流分析及程序bug解决； 部分现场自学习检测训练优化                                                                                                                                               |
| 4        | 串EL检测中的软件结构设计、算法编写(分割拼接图像，深度学习模块)、光学成像和软件整合、现场调试                         | 程序编写完成，自学习训练完成，待客户端验证检测效果                                                                                                                                                                                                           |
| 5        | 软件组中软件开发方案制定,软件团队日常工作管理                                                  | 合理分配工作，保证软件组各项工作顺利、高效完成                                                                                                                                                                                                             |
| 6        | 激光二维码解码的前期调研工作                                                           | 二维码识别算法方案研究 ，成像方案研究                                                                                                                                                                                                                 |
| 2020全年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 开发能够实现缺陷面积检测的神经网络                                                        | 在类单晶上测试，检测效果与海康威视、猎户座的检测效果差不多，因类单晶改造项目暂停，暂未在客户端使用                                                                                                                                                                                   |
| 2        | 分类神经网络检测速度优化提升, 以适应高分辨率检测                                                | 深度学习检测模块实现7倍左右的检测时间提升，增加分块数量以后在满足更高检测精度的同时也能满足生产要求。该优化已部署到现场应用。                                                                                                                                                                     |
| 3        | 软件组工作中遇到的关键技术难点解决                                                        | 3D翘曲数据分析、算法研究；3D判定硅片线痕方向算法；软件锁机功能设计；上位机卡顿分析及解决；TCP通信优化；部分现场直流分析及程序bug解决； 部分现场自学习训练优化；脏污分块程序速度优化等等                                                                                                                                   |
| 4        | 串EL检测中的软件结构设计、算法编写(分割拼接图像，深度学习模块)、光学成像和软件整合、现场调试                         | EL检测模组在扬州晶澳、浙江隆基试用，目前检测效果满足客户需求，已实现自动检。                                                                                                                                                                                             |
| 5        | 软件组中软件开发方案制定,软件团队日常工作管理                                                  | 合理分配工作，保证软件组各项工作顺利、高效完成                                                                                                                                                                                                             |
| 6        | 尺寸模组测量重复性提升及尺寸模组其他问题解决                                                   | 经分析、测试，发现皮带对尺寸重复性影响很大，同时在程序上做了优化改进，接下来更新到现场测试；尺寸标定还存在问题，继续解决                                                                                                                                                                        |
| 2021半年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 软件组工作中遇到的关键技术难点解决                                                        | 协助重点解决3D直流、崩溃、超时等问题，直流、超时问题已解决，崩溃问题还偶有发生；视觉软件崩溃及超时问题； 深度学习训练方法优化。                                                                                                                                                                   |
| 2        | 标定板测试及增加尺寸程序的标定板标定功能，尺寸检测重复性优化                                           | 程序编写完成，标定板经测试标定更加精准，尺寸重复性目前能达到客户要求 。                                                                                                                                                                                                |
| 3        | 解决各现场尺寸工位的其他问题                                                           | 已知问题基本解决                                                                                                                                                                                                                            |
| 4        | 基于尺寸、面积的语义分割网络测试                                                         | 确定整体方案，解决关键技术问题，把握执行方向及进度，其大部分细节执行过程由方浩完成，目前已完成程序的训练和测试，与视觉软件的对接，检测效果初步判断良好，需再多做些测试                                                                                                                                                 |
| 5        | 场内部分现场的深度学习训练优化,出差(阿特斯，眉山，金堂)了解机器情况，解决现场问题                               | 已知问题已解决，基本满足现场的对标及生产要求                                                                                                                                                                                                              |
| 6        | 软件组中软件开发相关的方案制定及把控，软件团队日常工作管理                                            | 对于关键技术方案，主导方案的制定及执行，合理分配工作，保证软件组各项工作顺利、高效完成                                                                                                                                                                                         |
| 2021全年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 解决软件组工作中遇到的关键技术难点                                                        | 协助重点解决3D直流、崩溃、超时等问题；视觉软件崩溃及超时问题； 深度学习检测提速及训练方法优化；                                                                                                                                                                                   |
| 2        | 标定板测试及增加尺寸程序的标定板标定功能，尺寸检测重复性问题排查及程序优化                                    | 程序编写完成，标定板经测试标定更加精准，尺寸重复性较差原因分析已解决                                                                                                                                                                                                  |
| 3        | 解决各现场尺寸工位的其他问题                                                           | 目前已知问题(如叠片、大小缺角、倒角崩边、塌边检测等)基本解决。                                                                                                                                                                                                    |
| 4        | 重点主导提升深度学习检测通用性及基于尺寸、面积的语义分割网络和目标检测网络测试                                  | 深度学习通用性及训练技巧有较大提升但暂未达到预期的目标，语义分割网络在崩边硅落检测上已实现并在高景使用，深度学习+传统算法方式已在多个现场使用；目标检测网络已在场内搭建测试，预计1~2个月后可到现场试用。                                                                                                                              |
| 5        | 光学及软件相关的标准化规范大纲及部分内容编写，标准化整体进度及内容把控                                      | 目前整体进度完成约80%，预计12月底全部完成。                                                                                                                                                                                                            |
| 6        | 软件开发相关的方案制定及进度把控，软件团队日常工作管理                                              | 对于关键技术方案，主导方案的制定及执行，合理分配工作，保证软件组各项工作顺利、高效完成。                                                                                                                                                                                        |
| 2022半年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 解决关键技术难点：                                                                | 1.5-6月份，大量现场数据分析，发现尺寸异常有规律的与花篮有关，从而将问题准确定位到温度相关，成为解决问题的关键；                                                                                                                                                                          |
| nan      | 1.高景尺寸测量超标问题；                                                            | 2.3-6月份，a.提升重复性的实验验证；b.尝试修改算法，如参数调优、多段拟合、开发多种边缘点提取算法等，效果提升不明显；c.连拍多张图的方案对重复性有明显提升（有数据支持），暂未应用到设备上，还需继续完成；                                                                                                                           |
| nan      | 2.尺寸测量重复性提升；                                                             | 3.3-6月份，a.提升准确性的实验验证;b.尝试标定算法的改进，目前有皮带遮挡的透射式标定方法初步实验测试可行，待应用到设备上；                                                                                                                                                                   |
| nan      | 3.尺寸测量准确性改善；                                                             | nan                                                                                                                                                                                                                                 |
| 2        | 解决常规现场问题：                                                                | 1.1-6月份，隆基、高景等现场直流较高的问题解决，已知问题已解决；                                                                                                                                                                                                  |
| nan      | 1.解决现场直流较高的问题                                                            | 2.1-6月份，多个现场的大缺角、小缺角、叠片、崩边检测的问题解决，已知问题已解决；                                                                                                                                                                                          |
| nan      | 2.尺寸检测问题处理；                                                              | 3.6月份，使用类似应材、天准的方案可解决擦伤问题，同时验证了该方案对于淡脏污、小白点的检测有较明显提升，已导入客户小批量验证；                                                                                                                                                                    |
| nan      | 3.晶科擦伤误判实验 ;                                                             | 4.1-6月份，主导分析问题，小部分为所使用的程序问题，已解决，主要通过更换皮带解决；                                                                                                                                                                                         |
| nan      | 4.解决部分现场尺寸测量重复性;                                                         | nan                                                                                                                                                                                                                                 |
| 3        | 新项目样机开发及调试：                                                              | 1.1-3月份，a.前期成像方案确定及元器件选型，成像方案实验验证;b.软件框架方案确定；c.提供数片算法方案；                                                                                                                                                                            |
| nan      | 1.051B项目；                                                                | 2.1-6月份，a.软件工作安排及部分必要的外围协助 ；b.部分视觉模组软件调试及分析；c.部分尺寸测量相关的测试;d.win10系统的安装及测试；                                                                                                                                                          |
| nan      | 2.018T项目；                                                                | 3. 2-6月份，尺寸、X侧边、脏污方案的确定(主导工作、方案确定及部分实验的实施)；                                                                                                                                                                                         |
| nan      | 3.018K项目；                                                                | nan                                                                                                                                                                                                                                 |
| 4        | 客户新需求开发：                                                                 | 1.3-5月份，对于宇泽、隆基的大小倒角片、有无倒角片的尺寸程序开发，已完成；                                                                                                                                                                                             |
| nan      | 1.尺寸检测兼容大小倒角，兼容有无倒角；                                                     | 2.3-5月份，主导检测方案的确定及完成关键的高效数据转图像的算法，已在高景测试；                                                                                                                                                                                           |
| nan      | 2.刻痕(密线)的深度学习检测；                                                         | 3.4-5月份，对宇泽、隆基添加边长、倒角、倒角投影的单独补偿值，已完成；                                                                                                                                                                                               |
| nan      | 3.尺寸补偿值                                                                  | 4.2-4月份，对接印度订单的硬件CE认证(软件相关物料)，已完成；                                                                                                                                                                                                  |
| nan      | 4.CE认证对接                                                                 | nan                                                                                                                                                                                                                                 |
| 5        | 软件团队管理工作：                                                                | 1. 3-4月份，主导方案评估及部分软件测试(a. opencv有2.4升级为4.5，可用新功能及部分函数效率提升；b. net4.5升级为net4.8；c. sqlite数据库评估,018T上使用; d. opencvGPU版本的使用，统一深度学习接口，兼容caffe/pytorch/tensorflow);                                                                        |
| nan      | 1.视觉程序结构升级                                                               | 2. 1-6月份，主导、安排部分硬件的选型及实验(货期原因及功能原因替换)，部分方案可行，部分暂时不可行;                                                                                                                                                                               |
| nan      | 2.新品牌替代                                                                  | 3. 1-6月份，新、老机型的BOM处理;                                                                                                                                                                                                               |
| nan      | 3.文职工作                                                                   | nan                                                                                                                                                                                                                                 |
| nan      | nan                                                                      | 4. 1-6月份，a.工作规划：了解行业及友商动态，预判未来可能的需求及可能出现的问题，提前准备及安排人员处理；b.对接客户:对于客户新需求及设备问题，与客户沟通，协调资源解决；c. 公司内跨专业跨部门沟通协调：协调资源解决本专业事项或者非本专业事项(包含技术及非技术)，确保项目顺利进行；d. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个人的工作量平衡、都能得到有效锻炼、整体软件工作能够顺利进行； |
| nan      | 4.工作管理                                                                   | nan                                                                                                                                                                                                                                 |
| nan      | 5.人员培养                                                                   | 5.1-6月份，重点关注俞任姣、蔡嘉辉的培养，曾超交由邵旭旭培养。目前a.蔡嘉辉能够完全接任训练工作，分选机视觉调试基本能熟练操作，硬件实验基本能熟练掌握，软件编写部分稍弱，下半年重点培养软件编程；b.俞任姣深度参与051项目的硬件及软件开发工作，同时对于分选机尺寸检测掌握程度较好，软硬件掌握均衡；俞任姣、蔡嘉辉两位同事近半年进步很大。                                                           |
| 2022全年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 软件管理工作：                                                                  | 1. 1-12月份，                                                                                                                                                                                                                          |
| nan      | 1.工作管理                                                                   | a.工作规划：了解行业及友商动态，预判未来可能的需求及可能出现的问题，提前准备及安排人员处理；                                                                                                                                                                                     |
| nan      | 2.人员培养                                                                   | b.对接客户：对于客户新需求及设备问题，与客户沟通，协调资源解决；                                                                                                                                                                                                   |
| nan      | 3.文职工作                                                                   | c. 公司内跨专业跨部门沟通协调：协调资源解决本专业事项或者非本专业事项(包含技术及非技术)，确保项目顺利进行；                                                                                                                                                                            |
| nan      | nan                                                                      | d. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个人的工作量平衡、都能得到有效锻炼、整体软件工作能够顺利进行；                                                                                                                                                  |
| nan      | nan                                                                      | 2.1-12月份，重点关注去年入职及今年入职人员的培养。目前蔡嘉辉能够完全接任训练工作、视觉调试工作，软件编写能完成常规任务，需继续历练；俞任姣深度参与051项目的硬件及软件开发工作，同时对于分选机尺寸检测掌握较好，能独立完成尺寸相关的开发工作；吉首阳硬件调试经验较丰富，软件编写及主动性待加强，明年重点关注及培养；姚宇航、韩天主要由3D人员安排，对他们的整体反馈较好，陈靓的擅长点主要在深度学习方向，期待他能做出突破性的成绩。              |
| nan      | nan                                                                      | 3. 1-12月份，各种机型的BOM处理;                                                                                                                                                                                                               |
| 2        | 主导关键技术研发：                                                                | 1. 1-12月份，主导方案评估及部分软件测试                                                                                                                                                                                                             |
| nan      | 1.视觉程序结构升级                                                               | a. opencv/.net升级；                                                                                                                                                                                                                   |
| nan      | 2.新品牌替代测试                                                                | b. sqlite、mysql数据库评估,在018T上使用；                                                                                                                                                                                                      |
| nan      | nan                                                                      | c. opencvGPU版本的使用，统一深度学习接口，兼容caffe/pytorch/tensorflow;                                                                                                                                                                              |
| nan      | nan                                                                      | d.python训练工具开发，caffe-pytorch模型互转；                                                                                                                                                                                                   |
| nan      | nan                                                                      | 2. 1-12月份，主导、安排部分硬件的选型及实验(货期原因及功能原因替换)，部分方案可行，部分暂时不可行;                                                                                                                                                                              |
| 3        | 新项目样机开发及调试：                                                              | 1.1-3月份，a.前期成像方案确定及元器件选型，成像方案实验验证;b.软件框架方案确定；c.提供数片算法方案；                                                                                                                                                                            |
| nan      | 1.051B项目；                                                                | 2.1-12月份，a.软件工作安排及部分必要的外围协助 ；b.部分视觉模组软件调试及分析；c.部分尺寸测量相关的测试;                                                                                                                                                                         |
| nan      | 2.018T项目；                                                                | 3. 2-7月份，尺寸、X侧边、脏污方案的确定(主导工作、方案确定及部分实验的实施)；                                                                                                                                                                                         |
| nan      | 3.018K项目；                                                                | nan                                                                                                                                                                                                                                 |
| 4        | 解决关键技术难点：                                                                | 1.5-10月份，大量现场数据分析，发现尺寸异常有规律的与花篮有关，从而将问题准确定位到温度相关，成为解决问题的关键；                                                                                                                                                                         |
| nan      | 1.高景尺寸测量超标问题；                                                            | 2.3-10月份，a.尝试修改算法，如参数调优、多段拟合、开发多种边缘点提取算法等，效果稍有提升；b.连拍多张图的方案对重复性有明显提升，已在多台机上试用，018T所有尺寸检测指标提升到30um；                                                                                                                                  |
| nan      | 2.尺寸测量重复性提升；                                                             | 3.3-9月份，皮带遮挡的标定板标定算法编写，已批量用在量产机台上，10月份后新投机台把标定板标定作为标配；                                                                                                                                                                              |
| nan      | 3.尺寸测量准确性改善；                                                             | 4.6-8月份， 一维数据转图片的代码编写，一张图能在1ms内完成，使基于深度学习的刻痕检测成为可能，目前在隆基试用，基本满足客户要求；                                                                                                                                                                |
| nan      | 4.刻痕检测中高效数据处理；                                                           | nan                                                                                                                                                                                                                                 |
| 5        | 解决常规现场问题：                                                                | 1.1-12月份，隆基、高景等现场直流较高的问题，工作站异常情况处理，已知问题已解决；                                                                                                                                                                                         |
| nan      | 1.解决现场直流较高的问题                                                            | 2.1-8月份，多个现场的大缺角、小缺角、叠片、崩边检测的问题解决，已知问题已解决；                                                                                                                                                                                          |
| nan      | 2.尺寸检测问题处理；                                                              | 3.6-10月份，使用类似应材、天准的方案可解决擦伤问题，同时验证了该方案对于淡脏污、小白点的检测有较明显提升，已导入该光源到量产版本；                                                                                                                                                                |
| nan      | 3.脏污检测提升实验 ;                                                             | 4.1-6月份，主导分析问题，小部分为所使用的程序问题，已解决，主要通过更换皮带解决；                                                                                                                                                                                         |
| nan      | 4.解决部分现场尺寸测量重复性;                                                         | nan                                                                                                                                                                                                                                 |
| 6        | 客户新需求开发：                                                                 | 1.3-5月份，对于宇泽、隆基的大小倒角片、有无倒角片的尺寸程序开发，已完成；                                                                                                                                                                                             |
| nan      | 1.尺寸检测兼容大、小倒角，兼容有、无倒角；                                                   | 2.3-5月份，主导检测方案的确定及完成关键的高效数据转图像的算法，已在高景测试；                                                                                                                                                                                           |
| nan      | 2.刻痕(密线)的深度学习检测；                                                         | 3.2-4月份，对接印度订单的硬件CE认证(软件相关物料)，已完成；                                                                                                                                                                                                  |
| nan      | 3.CE认证对接                                                                 | nan                                                                                                                                                                                                                                 |
| 2023半年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 软件管理工作：                                                                  | 1. 1-6月份，                                                                                                                                                                                                                           |
| nan      | 1.管理工作                                                                   | a.工作规划：了解行业及友商动态，预判未来可能的需求及可能出现的问题，提前做好技术储备及人员安排；                                                                                                                                                                                   |
| nan      | 2.人员培养                                                                   | b.对接客户：对于客户新需求及设备问题，积极与客户沟通，协调各方资源满足客户要求；                                                                                                                                                                                           |
| nan      | 3.文职工作                                                                   | c. 公司内跨专业跨部门沟通协调：协调资源解决本专业事项和协助解决非本专业事项(包含技术及非技术)，确保项目顺利进行；                                                                                                                                                                         |
| nan      | nan                                                                      | d. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个组员的工作量平衡并能得到有效锻炼，整体软件工作能够顺利进行；                                                                                                                                                  |
| nan      | nan                                                                      | 2.1-6月份，重点关注去年入职人员的培养。                                                                                                                                                                                                              |
| nan      | nan                                                                      | a.吉首阳硬件调试经验较丰富，软件编写及主动性待加强，上半年在硬件和软件上适当给他增加压力及必要的协助，目前他对项目和技术的理解及应用有明显提升；                                                                                                                                                           |
| nan      | nan                                                                      | b.姚宇航、韩天主要由李业、世娟安排工作，同时我对其培养方向持续关注，必要时加以纠正，整体上两位新人能力不错，进步很快。                                                                                                                                                                        |
| nan      | nan                                                                      | 3. 1-6月份，处理各种机型必要的BOM;                                                                                                                                                                                                              |
| 2        | 主导关键技术研发：                                                                | 1. 1-6月份，主导关键技术的方案评估                                                                                                                                                                                                                |
| nan      | 1.3D及深度学习方向研发                                                            | a. 3D三合一新程序的开发，从界面布局、程序框架、算法结构设置及整合方面加以指导，目前总体框架基本完成，详细代码待编写；                                                                                                                                                                       |
| nan      | 2.视觉器件降本测试                                                               | b. 3D线痕标片制作的方案评估，主导激光雕刻、化学刻蚀、线痕标准制定的研发过程，晶圆化学刻蚀的精度符合预期，待进一步测试；                                                                                                                                                                      |
| nan      | nan                                                                      | c. 3D调试片的制作方法及应用，采用在硅片边缘激光雕刻三角形的方式来评价硅片的偏移，已完成，待向生产工程培训推广；                                                                                                                                                                          |
| nan      | nan                                                                      | d.主导深度学习降本增效方案的研发，30系列显卡、轻量化网络、CS架构模式研究；                                                                                                                                                                                            |
| nan      | nan                                                                      | 2. 1-6月份，主导、安排部分硬件的选型及实验(货期原因及功能原因替换)，部分方案可行，部分暂时不可行;                                                                                                                                                                               |
| 3        | 新项目样机开发及调试：                                                              | 1.1-2月份，乐山高测现场值机，确保首次样机在客户顺利投产；                                                                                                                                                                                                     |
| nan      | 1.AM018T项目；                                                              | 2.1-6月份，a.软件工作安排及部分必要的外围协助 ；b.部分视觉模组软件调试及分析；                                                                                                                                                                                        |
| nan      | 2.AM018TA/C项目；                                                           | 3.3-6月份，双轨检测模组方案的评估(主导工作、方案确定及部分实验的实施)；                                                                                                                                                                                             |
| nan      | 3.AM018HA项目；                                                             | nan                                                                                                                                                                                                                                 |
| 4        | 解决关键技术难点：                                                                | 1.1-3月份，通过在现场大量的数据分析，对于倒角线痕的检测提供多项思路，基本解决高景倒角线痕漏检的问题；                                                                                                                                                                               |
| nan      | 1.高景倒角线痕漏检问题；                                                            | 2.5-6月份，主导3D人员分析、解决通信超时的可能原因，目前暂未解决，初步判断与短时间内高频通信有关，已定下测试方案待测试；                                                                                                                                                                     |
| nan      | 2.TC机型通信超时问题；                                                            | nan                                                                                                                                                                                                                                 |
| 5        | 解决常规现场问题：                                                                | 1.1-6月份，隆基、高景等现场直流较高的问题，工作站异常情况处理，已知问题已解决；                                                                                                                                                                                          |
| nan      | 1.解决现场直流较高的问题                                                            | 2.1-6月份，主导分析问题，小部分为所使用的程序问题，已解决，主要通过更换皮带解决；                                                                                                                                                                                         |
| nan      | 2.解决部分现场尺寸测量重复性;                                                         | nan                                                                                                                                                                                                                                 |
| 6        | 客户新需求开发：                                                                 | 1.1-6月份，对接隆基、高测等客户的上位机新需求，并于上位机人员确定方案，已完成；                                                                                                                                                                                          |
| nan      | 1.上位机各项新需求对接；                                                            | 2.1-6月份，韩华检测及上位机相关(数据分析，SECS/GEM)的需求对接，已知需求已对接；康宁检测及软件相关的需求对接，已知需求已对接；                                                                                                                                                              |
| nan      | 2.海外项目的新需求对接；                                                            | nan                                                                                                                                                                                                                                 |
| 2023全年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 软件管理工作：                                                                  | 1.1-12月份，梯队建设人员培养                                                                                                                                                                                                                   |
| nan      | 1.梯队建设人员培养                                                               | a.实施软件人员分组，通过人事在OA架构上进行调整，完全放权并赋予组长应有的权限，组长可执行人员管理、工作安排、绩效考核评优等等，同时对组长的工作加以辅导培养，使其今后能够承担更大的任务。                                                                                                                                      |
| nan      | 2.管理工作                                                                   | b.今年入职3名新同事，汪斌斌属校招由方浩带教，宗观林、王兵属社招有组长直接管理，重点关注他们的工作状态及工作质量，并在必要时加以指正，目前总体表现良好                                                                                                                                                        |
| nan      | nan                                                                      | 2. 1-12月份，管理工作                                                                                                                                                                                                                      |
| nan      | nan                                                                      | a.对接客户：对于客户新需求及设备问题，积极与客户沟通，协调各方资源满足合理的客户要求；                                                                                                                                                                                        |
| nan      | nan                                                                      | b. 公司内跨专业跨部门沟通协调：协调资源解决项目中软件相关的各项工作，确保项目顺利进行；                                                                                                                                                                                       |
| nan      | nan                                                                      | c. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个组员的工作量平衡并能得到有效锻炼，整体软件工作能够顺利进行；                                                                                                                                                  |
| nan      | nan                                                                      | d.重点工作亲自跟进并给予必要的协助，确保项目顺利推进                                                                                                                                                                                                         |
| 2        | 主导关键技术研发：                                                                | 1. 1-12月份，主导关键技术的方案评估                                                                                                                                                                                                               |
| nan      | 1.3D技术研发                                                                 | a. 3D三合一新程序的开发，从界面布局、程序框架、算法结构设置及整合方面加以指导，目前总体框架基本完成，详细代码正在编写，预计1.30日前上机测试；                                                                                                                                                         |
| nan      | 2.深度学习方向研发                                                               | b. 3D线痕标片制作的方案评估，激光雕刻、化学刻蚀、线痕标准制定的研发过程，目前动态测量线痕与marsurf对标正在进行，预计12月底完成；激光雕刻方案正在方案论证中；                                                                                                                                               |
| nan      | 3.视觉器件降本测试                                                               | c. 3D调试片的制作方法及应用，采用在硅片边缘激光雕刻三角形的方式来评价硅片的偏移，已完成，已向生产工程培训推广；                                                                                                                                                                          |
| nan      | nan                                                                      | d.主导深度学习降本增效方案的研发，30系列显卡替换测试已完成，随时可替换；CS架构模式研究，通信时间较长，实时性不满足要求；目标检测网络应用，目前隐裂、脏污均达到批量替换要求，硅落、崩边功能完善，存在少量漏判误判，计划年底可以批量部署；                                                                                                             |
| nan      | nan                                                                      | 2. 1-12月份，主导、安排部分硬件的选型及实验(货期原因及功能原因替换)，已完成部分光源、相机、采集卡等的替换，降本幅度很大，使得分选机更有竞争力                                                                                                                                                         |
| 3        | 新项目样机开发及调试：                                                              | 1.1-6月份，负责韩华项目的前期对接及方案确认，后交由邵旭旭主导负责，目前韩华样机处于调试阶段；                                                                                                                                                                                   |
| nan      | 1.A018QA项目；                                                              | 2.1-12月份，TC机器已经量产，属于今年主力机型；TH高速机在隆基试用，总体表现良好，马上批量投产                                                                                                                                                                                 |
| nan      | 2.A018TH/C项目；                                                            | 3.3-12月份，双轨检测模组方案的评估(主导工作、方案确定及部分实验的实施)；                                                                                                                                                                                            |
| nan      | 3.A018HA项目；                                                              | nan                                                                                                                                                                                                                                 |
| 4        | 解决关键技术难点：                                                                | 1.1-3月份，通过在现场大量的数据分析，对于倒角线痕的检测提供多项思路，基本解决高景倒角线痕漏检的问题；                                                                                                                                                                               |
| nan      | 1.高景倒角线痕漏检问题；                                                            | 2.5-6月份，主导3D人员分析、解决通信超时问题，目前通过LMI轮廓模式和闭环逻辑优化，问题已解决；                                                                                                                                                                                 |
| nan      | 2.TC机型通信超时问题；                                                            | 3.8-11月份，通过PN数据分析、PN工具源码反编译及参数修改等措施，PN误判问题得到大大缓解并且可控；                                                                                                                                                                               |
| nan      | 3.PN误判改善                                                                 | 4.10-11月份，通过数据分析，确定为LMI本身问题，已通过程序优化，避免该问题再次出现，已解决；                                                                                                                                                                                  |
| nan      | 4.厚度异常问题                                                                 | nan                                                                                                                                                                                                                                 |
| 5        | 客户新需求开发：                                                                 | 1.1-12月份，对接隆基、高测等客户的上位机、视觉新需求，并与相关人员确定方案，已完成；                                                                                                                                                                                       |
| nan      | 1.各项新需求对接；                                                               | 2.1-6月份 海外项目                                                                                                                                                                                                                        |
| nan      | 2.海外项目的新需求对接；                                                            | a.韩华检测及上位机相关(数据分析，SECS/GEM)的需求对接，后转交邵旭旭负责；                                                                                                                                                                                          |
| nan      | nan                                                                      | b.康宁检测及软件相关的需求对接，已知需求已对接,并完成定制化改造；                                                                                                                                                                                                  |
| 2024半年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 主导关键技术研发：                                                                | 1.主导锁机程序的方案及解决关键的技术难点，完成部分代码编写，目前锁机程序已经部署到客户端                                                                                                                                                                                       |
| nan      | 1.锁机程序方案设计及部分程序开发                                                        | 2.主导优化winform程序多语言切换的方案，预估增加小语种的开发工作量降为原来的1/5，目前ＤＥＭＯ已验证，正在逐步替换原有多语言方案                                                                                                                                                               |
| nan      | 2.软件多语言切换的快速开发                                                           | 3.主导程序开发的方案、进度和编写部分卡壳的代码。目前还处于程序编写阶段，按计划处于延期状态，原因有１．开发人员对新程序框架理解不到位，上半年又经过反复讨论，同时对程序框架做了改进；２．上半年开发人员其他任务事项较多，该任务没做为紧急事项；                                                                                                            |
| nan      | 3.3D技术研发                                                                 | 4.主导红外测温方案及后期电阻率温补方案，目前红外测温能够精确测量硅片温度，用于硅片温度预警优于原温度探头，用于电阻率做温补还需要验证                                                                                                                                                                 |
| nan      | 4.红外测温探头及电阻率温补开发                                                         | 5.目标检测及语义分割网络应用，目前隐裂、脏污均达到批量替换要求，硅落、崩边经测试也达到客户检测要求，正在批量部署；                                                                                                                                                                          |
| nan      | 5.深度学习方向研发                                                               | 6..net代码混淆方案，用于防止程序被反编译，对比多种混淆工具的效果，最终确定Obfuscar的方案，正在要求各软件逐步部署                                                                                                                                                                     |
| nan      | 6..net代码混淆                                                               | nan                                                                                                                                                                                                                                 |
| 2        | 重点项目研发工作：                                                                | 1.确定动态密码和锁机程序结合的方案，微信端委托维因特开发，核心算法及设备端程序自己开发，目前开发已完成，正在测试                                                                                                                                                                           |
| nan      | 1.动态密码方案设计及程序开发                                                          | 2.完成康宁要求的软件细节优化、授权规范和文档要求，已达到验收标准                                                                                                                                                                                                   |
| nan      | 2.康宁厂内FAT验收                                                              | 3.CE认证已与供应商、标准化对接完成，客户细化需求已处理完成，BOM处理                                                                                                                                                                                               |
| nan      | 3.法国ＣＥ认证及特殊需求对接                                                          | 4.评估并制定利旧方案，实施过程中提供必要的协助，方案已完成                                                                                                                                                                                                      |
| nan      | 4.隆基利旧方案评估设计                                                             | 5.TH高产能对软件要求的评估和关键技术难点的支持，HA样机调试及部分问题解决，已完成                                                                                                                                                                                         |
| nan      | 5.ＴＨ量产及ＨＡ样机                                                              | nan                                                                                                                                                                                                                                 |
| 3        | 客户对接：                                                                    | 1.对接视觉、３Ｄ、上位机的客户需求，与组内同事商讨并把关方案，尤其上位机需求较多，已知事项已完成                                                                                                                                                                                   |
| nan      | 1.客户关于软件需求的对接及方案确认；                                                      | 2.专指常规问题的解决，已知问题已完成                                                                                                                                                                                                                 |
| nan      | 2.客户现场及在制车间问题处理                                                          | nan                                                                                                                                                                                                                                 |
| 4        | 软件管理工作                                                                   | 1.对接客户：对于客户新需求及设备问题，积极与客户沟通，协调各方资源满足合理的客户要求；                                                                                                                                                                                        |
| nan      | nan                                                                      | 2. 公司内跨专业跨部门沟通协调：协调资源解决项目中软件相关的各项工作，确保项目顺利进行；                                                                                                                                                                                       |
| nan      | nan                                                                      | 3. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个组员的工作量平衡并能得到有效锻炼，整体软件工作能够顺利进行；                                                                                                                                                  |
| nan      | nan                                                                      | 4.重点工作亲自跟进并给予必要的协助，确保项目顺利推进                                                                                                                                                                                                         |
| nan      | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 2024全年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 主导关键技术研发(权重>30%，分2项填写)：                                                  | 1. 开发工作已全部完成，其中厂外018F和018T系列机型大部分已部署，厂内发货新机默认部署；051系列也开发完成(汇川PLC)，厂外测试已完成，可视机部署。                                                                                                                                                    |
| nan      | 1. 设备锁机功能开发                                                              | nan                                                                                                                                                                                                                                 |
| nan      | 1.1 锁机总体方案设计，包括方案流程、模块规划，重点考虑隐蔽性、通用性和防破解性能                               | 2. 开发工作已全部完成，预估增加小语种时的开发工作量降为原来的1/5，目前已在上位机、3D、视觉软件上面实施完成                                                                                                                                                                           |
| nan      | 1.2 数据加密解密dll和授权工具的程序编写                                                  | nan                                                                                                                                                                                                                                 |
| nan      | 1.3 协助俞任姣完成其余的程序编写和测试"                                                   | 3.1 原3D团队成员对程序总体架构设计、算法性能优化、细分模块设计的能力较弱，通过带帮传的模式，相信他们在这方面的能力得到了提升                                                                                                                                                                   |
| nan      | 2. 优化软件多语言切换的方案                                                          | 3.2 目前3D新程序的开发和厂内测试已完成，正在协调客户到现场试用，预计12月中下旬可在客户现场试用，预计明年3月份可以批量部署"                                                                                                                                                                  |
| nan      | 2.1 分析现有软件多语言开发的弊端及痛点，主导设计新的软件多语言开发方案                                    | nan                                                                                                                                                                                                                                 |
| nan      | 2.2 方案的代码实施主要由从洪亮、宗关林完成，协助他们修复bug和细节优化"                                  | nan                                                                                                                                                                                                                                 |
| nan      | 3. 3D新程序开发及老程序性能优化                                                       | nan                                                                                                                                                                                                                                 |
| nan      | 3.1 主导3D新程序的程序架构设计 ，包括界面架构设计、算法结构设计和接口设计                                 | nan                                                                                                                                                                                                                                 |
| nan      | 3.2 关键技术难点的方案细化、代码编写和测试验证                                                | nan                                                                                                                                                                                                                                 |
| nan      | 3.3 兼顾人员技能培养                                                             | nan                                                                                                                                                                                                                                 |
| nan      | 3.4 其余代码的编写和测试由张世娟、俞任姣等完成                                                | nan                                                                                                                                                                                                                                 |
| 2        | 主导关键技术研发(权重>30%，分2项填写)：                                                  | 4. 算法库、设备端、服务器端、小程序端的开发工作已经全部完成，目前已在5个客户现场测试半个月，暂无问题，预计1.31之前完成批量部署                                                                                                                                                                 |
| nan      | 4. 通用型动态密码登录的功能开发                                                        | nan                                                                                                                                                                                                                                 |
| nan      | 4.1 动态密码登录的总体方案设计，包括设备端、服务器端、小程序端                                        | 5.1 深度学习代码复盘已完成，后续也存在持续开发新功能、优化性能的可能性                                                                                                                                                                                               |
| nan      | 4.2 兼容设备锁机功能整合的设计，考虑整合功能的隐蔽性、通用性和防破解性能                                   | 5.2 深度学习的通用接口开发正在进行，预计12月底可完成                                                                                                                                                                                                       |
| nan      | 4.3 登录密码和锁机密码的算法程序编写，并封装为dll供设备程序和小程序调用                                  | 5.3 深度学习训练部分的代码整合正在进行，预计1月底可完成 "                                                                                                                                                                                                    |
| nan      | 4.4 微信小程序功能开发和维护(前期由维因特开发，后来转由我开发)                                       | nan                                                                                                                                                                                                                                 |
| nan      | 4.5 协助从洪亮完成服务端程序开发和上位机人员完成设备端程序开发"                                       | nan                                                                                                                                                                                                                                 |
| nan      | 5. 深度学习相关代码整合以及性能优化、BUG修复                                                | nan                                                                                                                                                                                                                                 |
| nan      | 5.1 组织深度学习相关人员复盘代码，找出问题以及解决方案，以提高代码的复用性、提高检测性能和准确性，提高开发效率、降低开发工作量，节省人工成本 | nan                                                                                                                                                                                                                                 |
| nan      | 5.2 基于TensorRT的深度学习通用推理接口开发，涵盖分类、目标检测、语义分割网络，同时兼容多batch、多分类              | nan                                                                                                                                                                                                                                 |
| nan      | 5.3 协助汪斌斌完成深度学习训练部分的代码整合                                                 | nan                                                                                                                                                                                                                                 |
| 3        | 重点项目研发工作：                                                                | 1. 检测时间有所缩短，暂时满足通威分选机交付时2W产能的要求，但是现场为了满足平均产能要求，将瞬时产能提高到2.1W，出现了一些不稳定性的直流，后面也解决了                                                                                                                                                     |
| nan      | 1. 基于018TH 2W产能的软件检测性能优化，在不增加硬件配置的情况下满足产能提升25%,针对视觉、                     | nan                                                                                                                                                                                                                                 |
| nan      | 3D程序提出了多项改进方案                                                            | 2. 已全部完成FAT，设备已发货                                                                                                                                                                                                                   |
| nan      | 2. 康宁厂内FAT验收                                                             | nan                                                                                                                                                                                                                                 |
| nan      | 2.1 按技术协议和康宁要求，软件做了多项细化优化工作                                              | 3. CE认证及客户需求已完成，设备已发货                                                                                                                                                                                                               |
| nan      | 2.2 规范商业软件授权和自编软件库文件引用的授权                                                | nan                                                                                                                                                                                                                                 |
| nan      | 2.3 康宁要求的文档编写和整理"                                                        | 4. 第一批利旧改造已完成，利旧改造后的机台已交付客户正常使用；第2批利旧改造正在对接中，预计1月初完成，4月份完成交付                                                                                                                                                                        |
| nan      | 3. 018TC法国订单                                                             | nan                                                                                                                                                                                                                                 |
| nan      | 3.1ＣＥ认证中软件相关物料的认证对接以及BOM变更                                               | nan                                                                                                                                                                                                                                 |
| nan      | 3.2 客户特定需求的对接及实施"                                                        | nan                                                                                                                                                                                                                                 |
| nan      | 4. 隆基现有机台软件物料利旧改造方案的评估及实施工作                                              | nan                                                                                                                                                                                                                                 |
| 4        | 客户需求及现场问题处理：                                                             | 1. 已知事项已完成                                                                                                                                                                                                                          |
| nan      | 1. 客户关于软件需求的对接、方案确认及 安排人员实施                                              | 2. 专指常规问题的解决，已知问题已完成                                                                                                                                                                                                                |
| nan      | 2. 客户现场问题的对接、关键技术难点指导及安排人员实施                                             | 3. 不增加硬件仅通过优化视觉程序，已经解决                                                                                                                                                                                                              |
| nan      | 3. 通威2.1W瞬时产能导致的视觉模组通信延迟和检测超时问题                                          | nan                                                                                                                                                                                                                                 |
| 5        | 软件团队管理工作                                                                 | 1. 对接客户：对于客户新需求及设备问题，积极与客户沟通，协调各方资源满足合理的客户要求；                                                                                                                                                                                       |
| nan      | nan                                                                      | 2. 公司内跨专业跨部门沟通协调：协调资源解决项目中软件相关的各项工作，确保项目顺利进行；                                                                                                                                                                                       |
| nan      | nan                                                                      | 3. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，                                                                                                                                                                                    |
| nan      | nan                                                                      | 保证每个组员的工作量平衡并能得到有效锻炼，整体软件工作能够顺利进行；                                                                                                                                                                                                  |
| nan      | nan                                                                      | 4. 重点工作亲自跟进并给予必要的协助，确保项目顺利推进                                                                                                                                                                                                        |
| 2025半年   | nan                                                                      | nan                                                                                                                                                                                                                                 |
| 1        | 主导关键技术研发：                                                                | "1.1. 工程日报的数据汇总、零部件维保、检测软件与上位机数据对接、上位机数据展示等功能已完成；                                                                                                                                                                                   |
| nan      | 1. 分选机数据分析功能开发                                                           | 1.2. 智能化的数据分析已有初步开发方向，正在开发中"                                                                                                                                                                                                        |
| nan      | 1.1 主导数据分析的功能、开发方向和大致方案                                                  | "2.1. 以开发完成，正在厂外小批量测试                                                                                                                                                                                                               |
| nan      | 1.2 负责人员协调和项目进度把控                                                        | 2.2. 初步方案已经确定，正在程序开发中                                                                                                                                                                                                               |
| nan      | 2. 设备电脑时间同步、程序批量更新、上位机用户权限                                               | 2.3. 用户可以很方便的精确控制各个模块的控件权限，已完成"                                                                                                                                                                                                     |
| nan      | 2.1 统一车间内设备之间时间自动同步，便于设备的日志分析和动态密码的时间校验                                  | "3.1. 梳理出约10项代码不规范处，以跟踪相关人员改进                                                                                                                                                                                                       |
| nan      | 2.2 统一车间内程序批量更新功能开发                                                      | 3.2. 多处性能优化，各模组综合检测时间减少约20ms"                                                                                                                                                                                                       |
| nan      | 2.3 上位机自定义用户权限的方案评估                                                      | nan                                                                                                                                                                                                                                 |
| nan      | 3. 视觉程序代码整理、性能优化                                                         | nan                                                                                                                                                                                                                                 |
| nan      | 3.1 主导梳理出视觉程序中代码不规范的地方                                                   | nan                                                                                                                                                                                                                                 |
| nan      | 3.2 主导梳理出视觉程序中检测速度可提升的地方                                                 | nan                                                                                                                                                                                                                                 |
| 2        | 技术开发与技术预研：                                                               | "1.1.已部署基于ollama+openweb的AI大模型，已在硅片研发人员中使用                                                                                                                                                                                          |
| nan      | "1. AI在工作中的应用                                                            | 1.2.测试了visual studio和vscode上的多个插件，已确定在vscode上可以比较好的实现自动化编程                                                                                                                                                                          |
| nan      | 1.1 部署本地AI大模型，提供网页版，可供大家使用，与外网隔绝，避免信息泄露                                  | 1.3.基于ollama的大模型系统提示词调优，经调优话，大模型对于代码编程的回答有较大提升，自动化编程工具调用时更加顺畅"                                                                                                                                                                      |
| nan      | 1.2 IDE开发软件中自动化编程插件测评                                                    | "2.1.深度学习代码复盘已完成，后续也存在持续开发新功能、优化性能的可能性                                                                                                                                                                                              |
| nan      | 1.3 AI大模型系统提示词调优"                                                        | 2.2.深度学习的通用接口开发已完成，小批量在厂外测试"                                                                                                                                                                                                        |
| nan      | "2.深度学习推理部分通用接口                                                          | "3.1.评估了gitolite和gitea，gitea存在配置繁琐，用户权限过大，存在代码泄露风险，已确定gitolite方案更优                                                                                                                                                                  |
| nan      | 2.1组织深度学习相关人员复盘代码，找出问题以及解决方案，以提高代码的复用性、提高检测性能和准确性，提高开发效率、降低开发工作量，节省人工成本  | 3.2.原git仓库的代码已经全部无损迁移完成"                                                                                                                                                                                                            |
| nan      | 2.2基于TensorRT的深度学习通用推理接口开发，涵盖分类、目标检测、语义分割网络，同时兼容多batch、多分类"              | 4.已知问题及合理需求已完成，设备扫码小程序二维码的功能正在开发                                                                                                                                                                                                    |
| nan      | "3.git代码管理评估、代码迁移                                                        | nan                                                                                                                                                                                                                                 |
| nan      | 3.1git代码管理工具多方案评估                                                        | nan                                                                                                                                                                                                                                 |
| nan      | 3.2原git服务器的代码迁移"                                                         | nan                                                                                                                                                                                                                                 |
| nan      | "4.通用型动态密码登录的功能优化及批量部署                                                   | nan                                                                                                                                                                                                                                 |
| nan      | 4.1跟踪批量部署情况，及时处理现场的问题                                                    | nan                                                                                                                                                                                                                                 |
| nan      | 4.2针对现场使用动态密码的问题和新需求，优化功能"                                               | nan                                                                                                                                                                                                                                 |
| 3        | 重点项目研发工作：                                                                | 1.梳理出多项降本措施在018TD上实施，帮忙项目达到降本目标                                                                                                                                                                                                     |
| nan      | nan                                                                      | 2.第一批利旧及改造已经交付，后续利旧和改造根据机台差异和以往经验实施                                                                                                                                                                                                 |
| nan      | 1. 018TD降本方案评估                                                           | 3.检测需求、数据分析方案评估，重点研究切割线质量检测的问题                                                                                                                                                                                                      |
| nan      | 2. 隆基改造订单前期方案评估及改造中的事项对接                                                 | nan                                                                                                                                                                                                                                 |
| nan      | 3. 076项目前期方案评估                                                           | nan                                                                                                                                                                                                                                 |
| 4        | 客户需求及现场问题处理：                                                             | 1.已知事项已完成                                                                                                                                                                                                                           |
| nan      | nan                                                                      | 2.专指常规问题的解决，已知问题已完成                                                                                                                                                                                                                 |
| nan      | 1.客户关于软件需求的对接、方案确认及 安排人员实施                                               | 3.对客户进行检测软件使用的培训，开发现场提出合理的需求，解决现场的问题，已完成                                                                                                                                                                                            |
| nan      | 2.客户现场问题的对接、关键技术难点指导及安排人员实施                                              | nan                                                                                                                                                                                                                                 |
| nan      | 3.康宁现场问题解决                                                               | nan                                                                                                                                                                                                                                 |
| 5        | 软件团队管理工作                                                                 | 1.对接客户：对于客户新需求及设备问题，积极与客户沟通，协调各方资源满足合理的客户要求；                                                                                                                                                                                        |
| nan      | nan                                                                      | nan                                                                                                                                                                                                                                 |
| nan      | nan                                                                      | 2. 公司内跨专业跨部门沟通协调：协调资源解决项目中软件相关的各项工作，确保项目顺利进行；                                                                                                                                                                                       |
| nan      | nan                                                                      | 3. 工作任务分发：根据组内人员的能力、发展方向、工作量、性格特点及工作内容的特性合理安排工作，保证每个组员的工作量平衡并能得到有效锻炼，整体软件工作能够顺利进行；                                                                                                                                                  |
| nan      | nan                                                                      | 4.重点工作亲自跟进并给予必要的协助，确保项目顺利推进                                                                                                                                                                                                         |

